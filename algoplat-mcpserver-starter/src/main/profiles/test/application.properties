#健康检测相关配置,如需改动请同步修改check.sh文件TEST_URL
management.server.port=8080
management.endpoints.web.base-path=/monitor
management.endpoints.web.path-mapping.health=/alive
#server相关
server.port=8080
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true
spring.main.banner-mode=off

zebra.sqlSessionFactory.plugin-bean-names=pageInterceptor 

spring.ai.openai.api-key=1790707665199575109
spring.ai.openai.model=gpt-4o-mini
spring.ai.openai.base-url=https://aigc.sankuai.com/v1/openai/native
spring.ai.openai.chat.completions-path=/chat/completions

spring.ai.mcp.server.enabled=true
spring.ai.mcp.server.name=bml-mcp-server
spring.ai.mcp.server.version=0.0.1
spring.ai.mcp.server.type=SYNC

spring.ai.mcp.server.stdio=false
spring.ai.mcp.server.resource-change-notification=true
spring.ai.mcp.server.tool-change-notification=true
spring.ai.mcp.server.prompt-change-notification=true
xuecheng.cookie.key=bml_agent_xuecheng_test_cookie_
xuecheng.appkey=6224061214003I14
org.appId=bb42b7b909
s3.hostname=msstest.vip.sankuai.com
s3.bucketName=mcpserver-s3
dx.key=55227M2002L14201
dx.pubId=137439764099
udb.tenant=sankuai.info

spring.servlet.multipart.max-file-size=1024MB
spring.servlet.multipart.max-request-size=1024MB

crane.domain=http://crane.inf.test.sankuai.com