package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao;

import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.ToolInfoPoExample;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.mapper.ToolInfoPoMapper;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.ToolInfoPo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.*;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.StatusEnum;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/7
 */

@Slf4j
@Service
public class ToolsInfoDao {
    @Resource
    private ToolInfoPoMapper toolInfoPoMapper;

    @Resource
    private PigeonToolInfoDao pigeonToolInfoDao;

    @Resource
    private HttpToolInfoDao httpToolInfoDao;

    @Resource
    private ThriftToolInfoDao thriftToolInfoDao;

    public ToolInfo getToolInfoByToolId(Long id) {
        ToolInfoPo toolInfoPo = toolInfoPoMapper.selectByPrimaryKey(id);
        return convertToToolInfo(toolInfoPo);
    }

    private ToolInfo convertToToolInfo(ToolInfoPo toolInfoPo) {
        ToolInfo toolInfo = new ToolInfo();
        BeanUtils.copyProperties(toolInfoPo, toolInfo);
        return toolInfo;
    }

    @Transactional
    public ToolInfo deleteToolInfoByToolName(String toolName, ToolTypeEnum type) {
        log.info("deleteToolInfoByToolName, toolName:{}, type:{}", toolName, type);
        ToolInfoPoExample toolInfoPoExample = new ToolInfoPoExample();
        toolInfoPoExample.createCriteria().andNameEqualTo(toolName).andStatusEqualTo(1);
        List<ToolInfoPo> toolInfoPos = toolInfoPoMapper.selectByExample(toolInfoPoExample);
        if (CollectionUtils.isEmpty(toolInfoPos)) {
            return null;
        }
        ToolInfoPo toolInfoPo = toolInfoPos.get(0);
        toolInfoPo.setStatus(0);
        Integer toolVersion = toolInfoPo.getToolVersion();
        if (toolVersion != null) {
            toolVersion++;
        }
        toolInfoPo.setToolVersion(toolVersion);
        toolInfoPo.setUpdateTime(new Date());
        toolInfoPoMapper.updateByPrimaryKeySelective(toolInfoPo);
        boolean delete = false;
        if (type == ToolTypeEnum.PIGEON) {
            Long pigeonToolId = toolInfoPo.getPigeonToolId();
            delete = pigeonToolInfoDao.deleteByToolId(pigeonToolId);
        } else if (type == ToolTypeEnum.HTTP) {
            Long httpToolId = toolInfoPo.getHttpToolId();
            delete = httpToolInfoDao.deleteById(httpToolId);
        } else if (type == ToolTypeEnum.THRIFT) {
            Long thriftToolId = toolInfoPo.getThriftToolId();
            delete = thriftToolInfoDao.deleteByToolId(thriftToolId);
        }
        if (!delete) {
            return null;
        }
        return convertToToolInfo(toolInfoPo);
    }

    public Long insert(ToolInfoPo toolInfoPo) {
        log.info("ToolsInfoDao.insert - 插入前的textType: {},description: {}", toolInfoPo.getTextType(), toolInfoPo.getDescription());
        toolInfoPoMapper.insertSelective(toolInfoPo);
        String toolUuid = toolInfoPo.getToolUuid();
        ToolInfoPo toolInfoPo1 = selectByToolUuid(toolUuid);
        if (toolInfoPo1 == null) {
            return null;
        }
        log.info("ToolsInfoDao.insert - 插入后查询到的textType: {},description: {}", toolInfoPo1.getTextType(), toolInfoPo1.getDescription());
        return toolInfoPo1.getId();
    }

    public ToolInfoPo selectByToolUuid(String toolUuid) {
        ToolInfoPoExample toolInfoPoExample = new ToolInfoPoExample();
        toolInfoPoExample.createCriteria().andToolUuidEqualTo(toolUuid).andStatusEqualTo(1);
        List<ToolInfoPo> toolInfoPos = toolInfoPoMapper.selectByExample(toolInfoPoExample);
        if (CollectionUtils.isEmpty(toolInfoPos)) {
            return null;
        }
        return toolInfoPos.get(0);
    }

    public List<ToolInfo> getToolInfoByMcpServerId(Long mcpServerId, StatusEnum statusEnum) {
        ToolInfoPoExample toolInfoPoExample = new ToolInfoPoExample();
        toolInfoPoExample.createCriteria().andMcpServerIdEqualTo(mcpServerId).andStatusEqualTo(statusEnum.getCode());
        List<ToolInfoPo> toolInfoPos = toolInfoPoMapper.selectByExample(toolInfoPoExample);
        if (CollectionUtils.isEmpty(toolInfoPos)) {
            toolInfoPos = new ArrayList<>();
        }
        List<ToolInfo> collect = toolInfoPos.stream().map(this::convertToToolInfo).collect(Collectors.toList());
        return collect;
    }

    public ToolInfo selectToolInfoById(Long toolId) {
        ToolInfoPoExample toolInfoPoExample = new ToolInfoPoExample();
        toolInfoPoExample.createCriteria().andIdEqualTo(toolId);
        List<ToolInfoPo> toolInfoPos = toolInfoPoMapper.selectByExample(toolInfoPoExample);
        if (CollectionUtils.isEmpty(toolInfoPos)) {
            return null;
        }
        ToolInfo toolInfo = convertToToolInfo(toolInfoPos.get(0));
        String type = toolInfo.getType();
        if (ToolTypeEnum.PIGEON.getValue().equals(type)) {
            PigeonToolInfo pigeonToolInfo = pigeonToolInfoDao.getPigeonToolInfoPoByToolId(toolInfo.getPigeonToolId());
            toolInfo = pigeonToolInfo.fillFieldToToolInfo(toolInfo);
        } else if (ToolTypeEnum.HTTP.getValue().equals(type)) {
            HttpToolInfo httpToolInfoByTool = httpToolInfoDao.getHttpToolInfoByToolId(toolInfo.getHttpToolId());
            toolInfo = httpToolInfoByTool.fillFieldToToolInfo(toolInfo);
        } else if (ToolTypeEnum.THRIFT.getValue().equals(type)) {
            ThriftToolInfo thriftToolInfo = thriftToolInfoDao.getThriftToolInfoByToolId(toolInfo.getThriftToolId());
            toolInfo = thriftToolInfo.fillFieldToToolInfo(toolInfo);
        } else {
            log.error("type error");
            throw new RuntimeException("type error");
        }
        return toolInfo;
    }

}
