package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/4
 */
public class LionKeys {

    public static final String RPC_INTERFACE_DESC_SYSTEM_PROMPT_TEMPLATE = "rpcInterfaceDescSystemPromptTemplate";
    public static final String LLM_API_KEY = "llmApiKey";
    public static final String MODEL_NAME = "llmModel";
    public static final String PARAMS_DESC_LLM = "paramsDescLLM";
    public static final String FRIDAY_SSO_ID = "fridaySsoId";
    public static final String TOOL_EXCEPTION_ANALYSIS = "toolExceptionAnalysis";
    public static final String MODEL_DATASET_UPLOAD_TIME_CONFIG = "modelDatasetUploadTimeConfig";
    public static final String MODEL_DEPLOY_TIME_CONFIG = "modelDeployTimeConfig";
    public static final String METADATA_IP = "metadataIp";
    public static final String PARSE_PLATFORM_IP = "parsePlatformIp";
    public static final String MATCH_OPS_IP = "matchOpsIp";
    public static final String METADATA_IP_9001 = "metadataIp9001";
    public static final String ZB_FLOW_PLATFORM_IP_9001 = "zbflowPlatformIp9001";
    public static final String CRANE_ROUTE_INFO = "craneRouteInfo";
}

