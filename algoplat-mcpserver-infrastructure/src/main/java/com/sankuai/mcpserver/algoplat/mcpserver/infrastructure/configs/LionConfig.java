package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs;

import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.sankuai.inf.dayu.dye.routing.config.RouteConfig;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.LionKeys;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.ServiceConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/4
 */
@Slf4j
@Configuration
public class LionConfig {
    public static String rpcInterfaceDescSystemPromptTemplate;

    public static String llmApiKey;

    public static String modelName;

    public static String paramsDescLLM;

    public static String fridaySsoId;

    public static Map<String, String> modelDatasetUploadTimeConfig;

    public static Map<String, String> modelDeployTimeConfig;


    public static String toolExceptionAnalysis;

    public static String metadataIp;

    public static String parsePlatformIp;

    public static String matchopsIp;

    public static String metadata9001;

    public static String zbflowPlatformIp9001;

    public static String craneRouteInfo;
    static {
        rpcInterfaceDescSystemPromptTemplate = Lion.getString(ServiceConstants.APP_KEY, LionKeys.RPC_INTERFACE_DESC_SYSTEM_PROMPT_TEMPLATE);
        paramsDescLLM = Lion.getString(ServiceConstants.APP_KEY, LionKeys.PARAMS_DESC_LLM);
        llmApiKey = Lion.getString(ServiceConstants.APP_KEY, LionKeys.LLM_API_KEY);
        modelName = Lion.getString(ServiceConstants.APP_KEY, LionKeys.MODEL_NAME);
        toolExceptionAnalysis = Lion.getString(ServiceConstants.APP_KEY, LionKeys.TOOL_EXCEPTION_ANALYSIS);
        ConfigRepository configRepository = Lion.getConfigRepository(ServiceConstants.APP_KEY);
        modelDatasetUploadTimeConfig = Lion.getMap(ServiceConstants.APP_KEY, LionKeys.MODEL_DATASET_UPLOAD_TIME_CONFIG, String.class, new HashMap<>());
        modelDeployTimeConfig = Lion.getMap(ServiceConstants.APP_KEY, LionKeys.MODEL_DEPLOY_TIME_CONFIG, String.class, new HashMap<>());
        metadataIp = Lion.getString(ServiceConstants.APP_KEY, LionKeys.METADATA_IP);
        parsePlatformIp = Lion.getString(ServiceConstants.APP_KEY, LionKeys.PARSE_PLATFORM_IP);
        matchopsIp = Lion.getString(ServiceConstants.APP_KEY, LionKeys.MATCH_OPS_IP);
        metadata9001 = Lion.getString(ServiceConstants.APP_KEY, LionKeys.METADATA_IP_9001);
        zbflowPlatformIp9001 = Lion.getString(ServiceConstants.APP_KEY, LionKeys.ZB_FLOW_PLATFORM_IP_9001);
        craneRouteInfo = Lion.getString(ServiceConstants.APP_KEY, LionKeys.CRANE_ROUTE_INFO);


        configRepository.addConfigListener(new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                if (configEvent.getKey().equals(LionKeys.RPC_INTERFACE_DESC_SYSTEM_PROMPT_TEMPLATE)) {
                    rpcInterfaceDescSystemPromptTemplate = Lion.getString(ServiceConstants.APP_KEY, LionKeys.RPC_INTERFACE_DESC_SYSTEM_PROMPT_TEMPLATE);
                } else if (configEvent.getKey().equals(LionKeys.LLM_API_KEY)) {
                    llmApiKey = Lion.getString(ServiceConstants.APP_KEY, LionKeys.LLM_API_KEY);
                } else if (configEvent.getKey().equals(LionKeys.MODEL_NAME)) {
                    modelName = Lion.getString(ServiceConstants.APP_KEY, LionKeys.MODEL_NAME);
                } else if (configEvent.getKey().equals(LionKeys.PARAMS_DESC_LLM)) {
                    paramsDescLLM = Lion.getString(ServiceConstants.APP_KEY, LionKeys.PARAMS_DESC_LLM);
                } else if (configEvent.getKey().equals(LionKeys.FRIDAY_SSO_ID)) {
                    fridaySsoId = Lion.getString(ServiceConstants.APP_KEY, LionKeys.FRIDAY_SSO_ID);
                }  else if(configEvent.getKey().equals(LionKeys.MODEL_DATASET_UPLOAD_TIME_CONFIG)){
                    modelDatasetUploadTimeConfig = Lion.getMap(ServiceConstants.APP_KEY, LionKeys.MODEL_DATASET_UPLOAD_TIME_CONFIG, String.class, new HashMap<>());
                } else if (configEvent.getKey().equals(LionKeys.MODEL_DEPLOY_TIME_CONFIG)) {
                    modelDeployTimeConfig = Lion.getMap(ServiceConstants.APP_KEY, LionKeys.MODEL_DEPLOY_TIME_CONFIG, String.class, new HashMap<>());
                } else if (configEvent.getKey().equals(LionKeys.TOOL_EXCEPTION_ANALYSIS)) {
                    toolExceptionAnalysis = Lion.getString(ServiceConstants.APP_KEY, LionKeys.TOOL_EXCEPTION_ANALYSIS);
                } else if (configEvent.getKey().equals(LionKeys.METADATA_IP)) {
                    metadataIp = Lion.getString(ServiceConstants.APP_KEY, LionKeys.METADATA_IP);
                } else if (configEvent.getKey().equals(LionKeys.PARSE_PLATFORM_IP)) {
                    parsePlatformIp = Lion.getString(ServiceConstants.APP_KEY, LionKeys.PARSE_PLATFORM_IP);
                } else if (configEvent.getKey().equals(LionKeys.MATCH_OPS_IP)) {
                    matchopsIp = Lion.getString(ServiceConstants.APP_KEY, LionKeys.MATCH_OPS_IP);
                } else if (configEvent.getKey().equals(LionKeys.METADATA_IP_9001)) {
                    metadata9001 = Lion.getString(ServiceConstants.APP_KEY, LionKeys.METADATA_IP_9001);
                } else if (configEvent.getKey().equals(LionKeys.ZB_FLOW_PLATFORM_IP_9001)) {
                    zbflowPlatformIp9001  = Lion.getString(ServiceConstants.APP_KEY, LionKeys.ZB_FLOW_PLATFORM_IP_9001);
                } else if (configEvent.getKey().equals(LionKeys.CRANE_ROUTE_INFO)) {
                    craneRouteInfo = Lion.getString(ServiceConstants.APP_KEY, LionKeys.CRANE_ROUTE_INFO);
                }
            }
        });
    }

}

