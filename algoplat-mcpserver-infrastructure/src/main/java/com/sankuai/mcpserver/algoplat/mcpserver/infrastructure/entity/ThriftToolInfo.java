package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity;


import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class ThriftToolInfo extends ToolInfo {

    private String appKey;

    private String interfaceName;

    private String methodName;

    private String cell;

    private String ip;

    private String port;

    public ToolInfo fillFieldToToolInfo(ToolInfo toolInfo) {
        this.setId(toolInfo.getId());
        this.setBusinessLine(toolInfo.getBusinessLine());
        this.setToolUuid(toolInfo.getToolUuid());
        this.setName(toolInfo.getName());
        this.setDescription(toolInfo.getDescription());
        this.setTextType(toolInfo.getTextType());
        this.setType(toolInfo.getType());
        this.setHttpToolId(toolInfo.getHttpToolId());
        this.setPigeonToolId(toolInfo.getPigeonToolId());
        this.setThriftToolId(toolInfo.getThriftToolId());
        this.setToolParams(toolInfo.getToolParams());
        this.setTimeOut(toolInfo.getTimeOut());
        this.setLlmGenDescription(toolInfo.getLlmGenDescription());
        this.setStatus(toolInfo.getStatus());
        this.setToolVersion(toolInfo.getToolVersion());
        this.setOwner(toolInfo.getOwner());
        this.setAddTime(toolInfo.getAddTime());
        this.setUpdateTime(toolInfo.getUpdateTime());
        this.setMcpServerId(toolInfo.getMcpServerId());
        this.setAvitorScriptCode(toolInfo.getAvitorScriptCode());
        this.setIsLongTermTask(toolInfo.getIsLongTermTask());
        this.setLongTermTaskType(toolInfo.getLongTermTaskType());
        return this;
    }
}