<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.mcpserver</groupId>
        <artifactId>algoplat-mcpserver</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>algoplat-mcpserver-domain</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>algoplat-mcpserver-domain</name>

    <dependencies>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>


        <dependency>
            <groupId>com.sankuai.algoplatform.llmpredict</groupId>
            <artifactId>predict-api</artifactId>
            <version>1.0.7</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- https://km.sankuai.com/page/58638366 -->
        <dependency>
            <groupId>com.sankuai.oceanus.http</groupId>
            <artifactId>oceanus-http</artifactId>
            <version>1.2.11</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>sts-sdk</artifactId>
            <version>0.4.0</version>
        </dependency>


        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.mcpserver</groupId>
            <artifactId>algoplat-mcpserver-infrastructure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
            <version>2.9.0.2</version>
        </dependency>

        <!-- 第三方依赖 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-crane</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.oceanus.registry</groupId>
            <artifactId>oceanus-registry</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.nib.data</groupId>
            <artifactId>zb-metadata-api</artifactId>
            <version>1.0.19-gj-agent-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.data</groupId>
            <artifactId>zb-parse-platform-api</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-mcp-server-spring-boot-starter</artifactId>
            <version>1.0.0-M6.MT2</version> <!-- 或最新版本 -->
        </dependency>
        <dependency>
        <groupId>com.meituan.spider</groupId>
        <artifactId>spider-database-api</artifactId>
        <version>2.1.44-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-openai</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-mcp-server-webmvc</artifactId>
        </dependency>

        <!-- Test dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>
</project>
