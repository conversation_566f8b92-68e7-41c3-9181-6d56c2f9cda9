package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * httpExecuteJob方法的简单单元测试
 * 这个测试类主要用于验证测试框架是否正常工作
 *
 * <AUTHOR>
 */
public class SimpleHttpExecuteJobTest {

    @Test
    public void testHttpExecuteJobMethodExists() {
        ChangeEventService.httpExecuteJob();
    }


}

