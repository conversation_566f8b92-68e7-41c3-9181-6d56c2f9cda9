package com.sankuai.mcpserver.algoplat.mcpserver.domain.register;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.McpBeanRecordService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.ServiceConstants;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.McpServerService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.BusinessLineDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.BusinessLine;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.McpServerEntity;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.entity.ToolRegisterContext;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.HttpToolInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class HttpToolRegister extends AbstractToolRegister {

    @Autowired
    private McpServerService mcpServerService;

    @Autowired
    private BusinessLineDao businessLineDao;

    @Autowired
    private McpBeanRecordService mcpBeanRecordService;

    private static final int CONNECTION_TIMEOUT = 5 * 1000;
    private static final int SO_TIMEOUT = 50 * 1000;
    private static final int CONN_REQUEST_TIMEOUT = 30 * 1000;
    private static final int MAX_PER_ROUTE = 300;
    private static final int MAX_CON_TOTAL = 2000;

    // 定义一个正则表达式来匹配HTTP链接
    private static final String HTTP_URL_PATTERN =
            "^(https?://)"  // 协议部分，支持http和https
                    + "("
                    + "((([a-zA-Z0-9\\-]+\\.)+[a-zA-Z]{2,}))" // 域名部分
                    + "|"
                    + "(((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))" // IP地址部分
                    + ")"
                    + "(:\\d+)?" // 可选的端口号
                    + "(/.*)?"   // 可选的路径和查询字符串
                    + "$";
    private static final HttpClient HTTP_CLIENT = HttpClients.custom().setDefaultRequestConfig(
                    RequestConfig.custom().setConnectTimeout(CONNECTION_TIMEOUT).setConnectionRequestTimeout(CONN_REQUEST_TIMEOUT).setSocketTimeout(SO_TIMEOUT).build())
            .setMaxConnPerRoute(MAX_PER_ROUTE).setMaxConnTotal(MAX_CON_TOTAL)
            .setRetryHandler(new DefaultHttpRequestRetryHandler(3, true)).build();

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public ToolCallback registerTool(ToolRegisterContext context, Long toolId) {
        HttpToolInfo httpToolInfo = transform(context, HttpToolInfo.class);
        context.setToolInfo(httpToolInfo);
        if (!isValidHttpUrl(httpToolInfo.getUrl())) {
            throw new RuntimeException("Invalid HTTP URL");
        }

        Long mcpServerId = httpToolInfo.getMcpServerId();
        McpServerEntity mcpServerById = mcpServerService.getMcpServerById(mcpServerId);
        if (mcpServerById == null) {
            throw new RuntimeException("未找到对应的McpServerEntity");
        }
        Long businessLineId = mcpServerById.getBusinessLineId();
        BusinessLine businessLineById = businessLineDao.getBusinessLineById(businessLineId);
        if (businessLineById == null) {
            throw new RuntimeException("未找到对应的BusinessLine");
        }
        ToolCallback toolCallback = getToolCallback(context);
        registerToolForMcpServer(toolCallback, businessLineById, mcpServerById);
        mcpBeanRecordService.updateAliveMcpRecord(mcpServerId, toolId);
        return toolCallback;

    }

    @Override
    public ToolCallback getToolCallback(ToolRegisterContext context) {
        HttpToolInfo httpToolInfo = transform(context, HttpToolInfo.class);
        context.setToolInfo(httpToolInfo);
        String toolName = httpToolInfo.getName();

        if (!isValidHttpUrl(httpToolInfo.getUrl())) {
            throw new RuntimeException("Invalid HTTP URL");
        }
        try {
            if (context.isHasRegistered()) {
                // 注销Tool
                unregisterTool(httpToolInfo);
            }
            // 注册Tool
            ToolCallback toolCallback = FunctionToolCallback
                    .builder(httpToolInfo.getName(), new HttpToolBiFunction(httpToolInfo, HTTP_CLIENT))
                    .description(httpToolInfo.getDescription())
                    .inputType(Map.class)
                    .inputSchema(generateInputSchema(httpToolInfo))
                    .build();
            return toolCallback;
        } catch (Exception e) {

            log.error("Failed to getToolCallback HTTP tool: {}", toolName, e);
            throw new RuntimeException("Failed to getToolCallback HTTP tool: " + e.getMessage(), e);
        }

    }

    public static boolean isValidHttpUrl(String url) {
        Pattern pattern = Pattern.compile(HTTP_URL_PATTERN);
        Matcher matcher = pattern.matcher(url);
        return matcher.matches();
    }

    @Override
    public ToolTypeEnum getToolType() {
        return ToolTypeEnum.HTTP;
    }

    @Slf4j
    private record HttpToolBiFunction(HttpToolInfo httpToolInfo,
                                      HttpClient httpClient) implements BiFunction<Map<String, Object>, ToolContext, String> {

        @Override
        public String apply(Map<String, Object> toolRequest, ToolContext toolContext) {
            Transaction t = Cat.newTransaction(ServiceConstants.JOB_NAME, "HttpToolRegister");
            String result = null;
            try {
                log.info("HttpToolBiFunction apply start.httpToolInfo:{}, toolRequest:{}", JSONObject.toJSONString(httpToolInfo), JacksonUtil.toJsonStrWithEmptyDefault(toolRequest));
                if (MapUtils.isNotEmpty(toolRequest) && toolRequest.containsKey("mcopilotContext")) {
                    toolRequest.remove("mcopilotContext");
                }
                result = switch (httpToolInfo.getMethodName()) {
                    case "GET" -> get(httpToolInfo.getUrl(), toolRequest, httpToolInfo.getHeaders());
                    case "POST" ->
                            post(httpToolInfo.getUrl(), JacksonUtil.toJsonStrWithEmptyDefault(toolRequest), httpToolInfo.getHeaders());
                    default -> throw new RuntimeException("暂不支持的HTTP方法类型");
                };
                if (result == null) {
                    log.info("HttpToolBiFunction apply result is null");
                    t.setStatus("HttpToolBiFunction apply result is null");
                    return null;
                }
                t.setSuccessStatus();
                log.info("HttpToolBiFunction apply end. toolRequest:{},result:{}", JacksonUtil.toJsonStrWithEmptyDefault(toolRequest), result);
                return result;
            } catch (Exception e) {

                t.setStatus(e);
                log.error("HttpToolBiFunction apply error.httpToolInfo:{},e:{}", JacksonUtil.toJsonStrWithEmptyDefault(httpToolInfo), e);
                return ExceptionUtils.getRootCauseMessage(e);
            } finally {
                log.info("HttpToolBiFunction apply finally, toolRequest:{},result:{}", JSONObject.toJSONString(toolRequest), JSONObject.toJSONString(result));
                t.complete();
            }
        }

        private String get(String url, Map<String, Object> params, Map<String, String> headerMap) throws Exception {
            StringBuilder generatedUrlStringBuilder = new StringBuilder(url);

            if (params != null && !params.isEmpty()) {
                boolean isFirst = true;
                for (Map.Entry<String, Object> entry : params.entrySet()) {
                    generatedUrlStringBuilder.append(isFirst ? "?" : "&");
                    String value = entry.getValue() instanceof String ?
                            (String) entry.getValue() :
                            JacksonUtil.toJsonStrWithEmptyDefault(entry.getValue());

                    generatedUrlStringBuilder.append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8.toString()))
                            .append("=")
                            .append(URLEncoder.encode(value, StandardCharsets.UTF_8.toString()));

                    isFirst = false;
                }
            }
            return get(generatedUrlStringBuilder.toString(), headerMap);
        }

        private String get(String url, Map<String, String> headerMap) throws Exception {
            HttpGet get = new HttpGet(url);
            if (MapUtils.isNotEmpty(headerMap)) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    get.setHeader(entry.getKey(), entry.getValue());
                }
            }
            return invoke(get);
        }

        public String post(String url, String json, Map<String, String> headerMap) throws Exception {
            StringEntity requestEntity = new StringEntity(json, "UTF-8");

            HttpPost post = new HttpPost(url);
            post.setEntity(requestEntity);
            post.setHeader("Content-type", "application/json");
            if (MapUtils.isNotEmpty(headerMap)) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    post.setHeader(entry.getKey(), entry.getValue());
                }
            }

            return invoke(post);
        }

        private String invoke(HttpUriRequest request) throws Exception {
            HttpResponse response = httpClient.execute(request);
            String body = parseResponse(response);

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == -1) {
                throw new ClientException(statusCode, "error");
            } else if (statusCode >= 400 && statusCode < 500) {
                throw new ClientException(statusCode, body);
            } else if (statusCode >= 500) {
                throw new ServerException(statusCode, body);
            }

            return body;
        }

        private String parseResponse(HttpResponse response) throws Exception {
            HttpEntity entity = response.getEntity();
            String body;
            try {
                body = EntityUtils.toString(entity);
            } catch (Exception e) {
                throw new Exception("parseResponse failure", e);
            }

            return body;
        }


        public static class ClientException extends RuntimeException {
            ClientException(int statusCode, String body) {
                super(("statusCode" + statusCode + ",body:" + body));
            }
        }

        public static class ServerException extends RuntimeException {
            ServerException(int statusCode, String body) {
                super(("statusCode" + statusCode + ",body:" + body));
            }
        }
    }
}
