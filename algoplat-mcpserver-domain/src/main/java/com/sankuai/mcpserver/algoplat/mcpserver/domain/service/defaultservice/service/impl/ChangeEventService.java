package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.AuthUtil;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mtrace.http.HttpClients;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.mcpserver.CustomMcpServerConfig;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.ToolRegister;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.ToolRegisterFactory;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.entity.ToolRegisterContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.RouteRule;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.McpServerService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.ToolManagerService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.others.BeanNameManager;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.others.ToolAnnotationCollector;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.aspect.CatTransaction;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs.KmsConfigs;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.BusinessLineDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.McpServerDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.ToolsInfoDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.AliveMcpRecord;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.BusinessLine;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.McpServerEntity;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ToolInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.StatusEnum;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import com.sankuai.meituan.common.time.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.mcpserver.algoplat.mcpserver.domain.constant.ServiceConstants.*;
import static com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs.LionConfig.craneRouteInfo;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/8/12
 */

@Slf4j
@Service
public class ChangeEventService {

    @Resource
    private ToolsInfoDao toolsInfoDao;

    @Resource
    private McpServerDao mcpServerDao;

    @Resource
    private McpServerService mcpServerService;

    @Resource
    private ToolManagerService toolManagerService;

    @Resource
    private BusinessLineDao businessLineDao;

    @Resource
    private CustomMcpServerConfig customMcpServerConfig;

    @Resource
    private McpBeanRecordService mcpBeanRecordService;

    @Resource
    private ToolAnnotationCollector toolAnnotationCollector;

    private static final HttpClient httpclient = HttpClients.createDefault();

    public void updateMcpBeanForChangeEvent() {
        ChangeEventStatistics statistics = new ChangeEventStatistics();
        try {
            // 1. 从数据库获取当前在线的工具信息
            Map<Long, AliveMcpRecord> dbRealTimeMcpRecord = getDbRealTimeMcpRecord();
            // 2. 对比两个Map，找出差异
            compareAndSyncMcpRecords(dbRealTimeMcpRecord, statistics);
        } finally {
            // 3. 打印统计信息
            statistics.logPrintDetailedStatistics();
        }
    }

    private Map<Long, AliveMcpRecord> getDbRealTimeMcpRecord() {
        Map<Long, AliveMcpRecord> dbRealTimeMcpRecord = new HashMap<>();
        List<McpServerEntity> mcpServerEntities = mcpServerDao.selectAllMcpServer();
        for (McpServerEntity mcpServer : mcpServerEntities) {
            Long mcpServerId = mcpServer.getId();
            List<ToolInfo> toolInfoByMcpServerId = toolsInfoDao.getToolInfoByMcpServerId(mcpServerId, StatusEnum.ACTIVE);
            AliveMcpRecord aliveMcpRecord = new AliveMcpRecord();
            aliveMcpRecord.setMcpServerId(mcpServerId);
            aliveMcpRecord.setAliveDefaultMcpToolNames(mcpServer.getDefaultToolNameList());
            Set<Long> toolIds = toolInfoByMcpServerId.stream().map(ToolInfo::getId).collect(Collectors.toSet());
            aliveMcpRecord.setAliveMcpToolIds(toolIds);
            dbRealTimeMcpRecord.put(mcpServerId, aliveMcpRecord);
        }
        return dbRealTimeMcpRecord;
    }

    /**
     * 对比数据库记录和内存记录，同步差异
     *
     * @param dbRealTimeMcpRecord 数据库中的实时记录
     * @param statistics          统计信息收集器
     */
    private void compareAndSyncMcpRecords(Map<Long, AliveMcpRecord> dbRealTimeMcpRecord, ChangeEventStatistics statistics) {
        Map<Long, AliveMcpRecord> memoryMcpRecords = mcpBeanRecordService.getAliveMcpRecords();

        // 设置基础统计信息
        statistics.setTotalMcpServersInDb(dbRealTimeMcpRecord.size());
        statistics.setTotalMcpServersInMemory(memoryMcpRecords.size());

        // 1 找出数据库中有但内存中没有的McpServer（需要创建）
        Set<Long> dbMcpServerIds = dbRealTimeMcpRecord.keySet();
        Set<Long> memoryMcpServerIds = memoryMcpRecords.keySet();

        Set<Long> toCreateMcpServerIds = new HashSet<>(dbMcpServerIds);
        toCreateMcpServerIds.removeAll(memoryMcpServerIds);

        // 2 找出内存中有但数据库中没有的McpServer（需要删除）
        Set<Long> toDeleteMcpServerIds = new HashSet<>(memoryMcpServerIds);
        toDeleteMcpServerIds.removeAll(dbMcpServerIds);

        // 3 找出两边都有的McpServer（需要对比内容）
        Set<Long> commonMcpServerIds = new HashSet<>(dbMcpServerIds);
        commonMcpServerIds.retainAll(memoryMcpServerIds);

        // 设置统计信息
        statistics.setMcpServersToCreate(toCreateMcpServerIds.size());
        statistics.setMcpServersToDelete(toDeleteMcpServerIds.size());
        statistics.setMcpServersToCompare(commonMcpServerIds.size());

        log.info("MCP同步检查结果: 需要创建={}, 需要删除={}, 需要对比={}", toCreateMcpServerIds.size(), toDeleteMcpServerIds.size(), commonMcpServerIds.size());

        // 6. 处理需要删除的McpServer
        for (Long mcpServerId : toDeleteMcpServerIds) {
            McpServerEntity mcpServerEntity = mcpServerDao.selectMcpServerById(mcpServerId);
            BusinessLine businessLineById = null;
            if (mcpServerEntity != null) {
                businessLineById = businessLineDao.getBusinessLineById(mcpServerEntity.getBusinessLineId());
            }

            // 添加差异记录
            statistics.addMcpServerDiff(mcpServerId,
                    mcpServerEntity != null ? mcpServerEntity.getMcpServerName() : "unknown",
                    businessLineById != null ? businessLineById.getBusinessLine() : "unknown",
                    ChangeEventStatistics.DiffType.DELETE);

            try {
                deleteMcpServer(mcpServerId);
                mcpBeanRecordService.removeAliveMcpRecord(mcpServerId);
                statistics.recordMcpServerDeleteSuccess(mcpServerId);
            } catch (Exception e) {
                log.error("删除McpServer失败: mcpServerId={}", mcpServerId, e);
                statistics.recordMcpServerDeleteFailure(mcpServerId, e.getMessage());
            }
        }

        // 6. 处理需要创建的McpServer
        for (Long mcpServerId : toCreateMcpServerIds) {
            McpServerEntity mcpServerEntity = mcpServerDao.selectMcpServerById(mcpServerId);
            BusinessLine businessLineById = businessLineDao.getBusinessLineById(mcpServerEntity.getBusinessLineId());
            if (businessLineById == null || mcpServerEntity == null) {
                statistics.recordMcpServerCreateFailure(mcpServerId, "业务线或McpServer不存在");
                continue;
            }
            statistics.addMcpServerDiff(mcpServerId, mcpServerEntity.getMcpServerName(), businessLineById.getBusinessLine(), ChangeEventStatistics.DiffType.CREATE);
            try {
                customMcpServerConfig.createMcpSyncServer(businessLineById, mcpServerEntity.getMcpServerName(), mcpServerId);
                statistics.recordMcpServerCreateSuccess(mcpServerId);
            } catch (Exception e) {
                log.error("创建McpServer失败: mcpServerId={}", mcpServerId, e);
                statistics.recordMcpServerCreateFailure(mcpServerId, e.getMessage());
            }
        }


        // 7. 处理需要对比内容的McpServer
        for (Long mcpServerId : commonMcpServerIds) {
            compareMcpServerContent(mcpServerId, dbRealTimeMcpRecord.get(mcpServerId), memoryMcpRecords.get(mcpServerId), statistics);
        }
    }


    /**
     * 对比McpServer的内容差异
     */
    private void compareMcpServerContent(Long mcpServerId, AliveMcpRecord dbRecord, AliveMcpRecord memoryRecord, ChangeEventStatistics statistics) {
        boolean hasChanges = false;
        boolean hasErrors = false;

        // 对比工具ID集合
        Set<Long> dbToolIds = dbRecord.getAliveMcpToolIds();
        Set<Long> memoryToolIds = memoryRecord.getAliveMcpToolIds();

        if (!dbToolIds.equals(memoryToolIds)) {
            hasChanges = true;
            // 找出需要新增的工具
            Set<Long> toAddToolIds = new HashSet<>(dbToolIds);
            toAddToolIds.removeAll(memoryToolIds);

            // 找出需要删除的工具
            Set<Long> toRemoveToolIds = new HashSet<>(memoryToolIds);
            toRemoveToolIds.removeAll(dbToolIds);

            // 设置工具差异信息
            statistics.setToolDiff(mcpServerId, dbToolIds, memoryToolIds, toAddToolIds, toRemoveToolIds);

            for (Long toolId : toRemoveToolIds) {
                try {
                    unregisterTool(toolId);
                    statistics.recordToolRemoveSuccess(mcpServerId, toolId);
                } catch (Exception e) {
                    log.error("注销工具失败: mcpServerId={}, toolId={}", mcpServerId, toolId, e);
                    statistics.recordToolRemoveFailure(mcpServerId, toolId, e.getMessage());
                    hasErrors = true;
                }
            }

            for (Long toolId : toAddToolIds) {
                try {
                    registerTool(toolId);
                    statistics.recordToolAddSuccess(mcpServerId, toolId);
                } catch (Exception e) {
                    log.error("注册工具失败: mcpServerId={}, toolId={}", mcpServerId, toolId, e);
                    statistics.recordToolAddFailure(mcpServerId, toolId, e.getMessage());
                    hasErrors = true;
                }
            }
        }

        // 对比默认工具名称列表
        List<String> dbDefaultToolNames = dbRecord.getAliveDefaultMcpToolNames();
        List<String> memoryDefaultToolNames = memoryRecord.getAliveDefaultMcpToolNames();

        if (!Objects.equals(dbDefaultToolNames, memoryDefaultToolNames)) {
            hasChanges = true;
            // 找出需要新增和删除的默认工具
            List<String> toAddDefaultTools = new ArrayList<>(dbDefaultToolNames != null ? dbDefaultToolNames : Collections.emptyList());
            toAddDefaultTools.removeAll(memoryDefaultToolNames != null ? memoryDefaultToolNames : Collections.emptyList());

            List<String> toRemoveDefaultTools = new ArrayList<>(memoryDefaultToolNames != null ? memoryDefaultToolNames : Collections.emptyList());
            toRemoveDefaultTools.removeAll(dbDefaultToolNames != null ? dbDefaultToolNames : Collections.emptyList());

            // 如果还没有创建差异记录，现在创建
            if (statistics.findMcpServerDiff(mcpServerId) == null) {
                // 获取McpServer信息用于统计
                McpServerEntity mcpServerEntity = mcpServerDao.selectMcpServerById(mcpServerId);
                BusinessLine businessLineById = null;
                if (mcpServerEntity != null) {
                    businessLineById = businessLineDao.getBusinessLineById(mcpServerEntity.getBusinessLineId());
                }

                statistics.addMcpServerDiff(mcpServerId,
                        mcpServerEntity != null ? mcpServerEntity.getMcpServerName() : "unknown",
                        businessLineById != null ? businessLineById.getBusinessLine() : "unknown",
                        ChangeEventStatistics.DiffType.UPDATE);
            }

            // 设置默认工具差异信息
            statistics.setDefaultToolDiff(mcpServerId,
                    dbDefaultToolNames != null ? dbDefaultToolNames : Collections.emptyList(),
                    memoryDefaultToolNames != null ? memoryDefaultToolNames : Collections.emptyList(),
                    toAddDefaultTools, toRemoveDefaultTools);

            try {
                updateDefaultToolForChangeEventServer(mcpServerId, memoryDefaultToolNames, dbDefaultToolNames, statistics);
            } catch (Exception e) {
                log.error("更新默认工具失败: mcpServerId={}", mcpServerId, e);
                hasErrors = true;
            }
        }

        // 如果有变更，记录UPDATE操作的最终结果
        if (hasChanges) {
            if (hasErrors) {
                statistics.recordMcpServerUpdateFailure(mcpServerId, "部分工具操作失败");
            } else {
                statistics.recordMcpServerUpdateSuccess(mcpServerId);
            }
        }
    }


    public void deleteMcpServer(Long mcpServerId) {
        McpServerEntity mcpServerById = mcpServerDao.selectMcpServerById(mcpServerId);
        BusinessLine businessLineById = businessLineDao.getBusinessLineById(mcpServerById.getBusinessLineId());
        if (mcpServerById == null || businessLineById == null) {
            log.error("业务线或mcpServer不存在，mcpServerId:{}", mcpServerId);
            throw new RuntimeException("业务线不存在");
        }

        List<ToolInfo> toolInfos = toolManagerService.getAllToolsInServer(mcpServerId);
        List<String> usedToolsNameInServer = toolManagerService.getUsedToolsNameInServer(mcpServerId);
        if(CollectionUtils.isEmpty(usedToolsNameInServer)) {
            return;
        }
        for (ToolInfo toolInfo : toolInfos) {
            if (usedToolsNameInServer.contains(toolInfo.getName())) {
                ToolRegister toolRegister = ToolRegisterFactory.toolRegisterMap.get(toolInfo.getType());
                if (ToolTypeEnum.SystemDefault.getValue().equals(toolInfo.getType())) {
                    mcpServerService.deleteSystemDefaultTool(mcpServerId, toolInfo.getName());
                    toolRegister.doUnregister(BeanNameManager.getMcpServerBeanNameByToolInfo(toolInfo), toolInfo.getName());
                    continue;
                }
                ToolInfo oldToolInfo = toolsInfoDao.selectToolInfoById(toolInfo.getId());
                String mcpServerBeanName = BeanNameManager.getMcpServerBeanNameByToolInfo(oldToolInfo);
                toolRegister.unregisterToolForMcpServer(mcpServerBeanName, toolInfo);
            }
        }
        customMcpServerConfig.destroyMcpSyncServer(businessLineById.getBusinessLine(), mcpServerById.getMcpServerName(), mcpServerId);
    }

    private void registerTool(Long newToolId) {
        ToolInfo toolInfo = toolsInfoDao.selectToolInfoById(newToolId);
        ToolRegisterContext context = new ToolRegisterContext();
        context.setToolInfoMap(toolInfo.convertToMap());
        context.setHasRegistered(toolManagerService.checkToolisExist(toolInfo.getMcpServerId(), toolInfo.getName()));
        context.setToolInfo(toolInfo);
        ToolRegister toolRegister = ToolRegisterFactory.toolRegisterMap.get(toolInfo.getType());
        toolRegister.registerTool(context, newToolId);
    }


    private void unregisterTool(Long toolId) {
        ToolInfo oldToolInfo = toolsInfoDao.selectToolInfoById(toolId);
        String mcpServerBeanName = BeanNameManager.getMcpServerBeanNameByToolInfo(oldToolInfo);
        ToolRegister toolRegister = ToolRegisterFactory.toolRegisterMap.get(oldToolInfo.getType());
        toolRegister.unregisterToolForMcpServer(mcpServerBeanName, oldToolInfo);
        mcpBeanRecordService.updateAliveMcpRecord(oldToolInfo.getMcpServerId(), toolId);
    }

    public void updateDefaultToolForChangeEventServer(Long mcpServerId, List<String> usedDefaultTools, List<String> defaultToolNameList, ChangeEventStatistics statistics) {
        McpServerEntity mcpServerEntity = mcpServerDao.selectMcpServerById(mcpServerId);
        Long businessLineId = mcpServerEntity.getBusinessLineId();
        BusinessLine businessLineById = businessLineDao.getBusinessLineById(businessLineId);
        List<FunctionCallback> defaultToolFunctionCallbacks = toolAnnotationCollector.getAtToolFunctionCallbacks(DefaultToolService.class);
        if (!org.springframework.util.CollectionUtils.isEmpty(defaultToolNameList)) {
            for (FunctionCallback defaultToolFunctionCallback : defaultToolFunctionCallbacks) {
                String name = defaultToolFunctionCallback.getName();
                if (defaultToolNameList.contains(name) && !usedDefaultTools.contains(name)) {
                    try {
                        mcpServerService.registerToolForMcpServer((ToolCallback) defaultToolFunctionCallback, businessLineById, mcpServerEntity);
                        mcpBeanRecordService.updateAliveMcpRecordForDefaultTool(mcpServerId, name);
                        statistics.recordDefaultToolAddSuccess(mcpServerId, name);
                    } catch (Exception e) {
                        log.error("注册默认工具失败: mcpServerId={}, toolName={}", mcpServerId, name, e);
                        statistics.recordDefaultToolAddFailure(mcpServerId, name, e.getMessage());
                    }
                } else if (!defaultToolNameList.contains(name) && usedDefaultTools.contains(name)) {
                    try {
                        String mcpServerBeanName = BeanNameManager.genMcpServerBeanName(businessLineById.getBusinessLine(), mcpServerEntity.getMcpServerName());
                        ToolRegister toolRegister = ToolRegisterFactory.toolRegisterMap.get(ToolTypeEnum.SystemDefault.getValue());
                        toolRegister.doUnregister(mcpServerBeanName, name);
                        mcpBeanRecordService.updateAliveMcpRecordForDefaultTool(mcpServerId, name);
                        statistics.recordDefaultToolRemoveSuccess(mcpServerId, name);
                    } catch (Exception e) {
                        log.error("注销默认工具失败: mcpServerId={}, toolName={}", mcpServerId, name, e);
                        statistics.recordDefaultToolRemoveFailure(mcpServerId, name, e.getMessage());
                    }
                }
            }
        }
    }


    @CatTransaction(type = "ChangeEventService", name = "httpExecuteJob")
    public static void httpExecuteJob() {
        try {
            HttpPost request = buildHttpRequest();
            String response = executeRequest(request);
            validateResponse(response);
            log.info("httpExecuteJob resData:{}", response);
        } catch (IOException e) {
            log.error("httpExecuteJob error", e);
            throw new RuntimeException(e);
        }
    }

    private static HttpPost buildHttpRequest() throws IOException {
        String craneUrl = MdpContextUtils.isProdEnv() ? CRANE_PROD_URL : CRANE_TEST_URL;
        HttpPost request = new HttpPost(craneUrl);
        setRequestHeaders(request);
        StringEntity entity = buildRequestEntity();
        request.setEntity(entity);
        return request;
    }

    private static void setRequestHeaders(HttpPost request) {
        String date = TimeUtil.getAuthDate(new Date());
        String authorization = AuthUtil.getAuthorization(CRANE_URI, "POST", date,
            APP_KEY, KmsConfigs.getKmsValueByKey(CRANE_SECRET_KMS));
        request.setHeader("Date", date);
        request.setHeader("Authorization", authorization);
    }

    private static StringEntity buildRequestEntity() {
        JSONObject jsonObject = new JSONObject();
        RouteRule routeRule = JSONObject.parseObject(craneRouteInfo, RouteRule.class);
        List<RouteRule> routeRuleList = Collections.singletonList(routeRule);
        jsonObject.put("routeRuleList", routeRuleList);
        StringEntity entity = new StringEntity(jsonObject.toString(), "UTF-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        return entity;
    }

    private static String executeRequest(HttpPost request) throws IOException {
        HttpResponse result = httpclient.execute(request);
        return EntityUtils.toString(result.getEntity());
    }

    private static void validateResponse(String response) {
        JSONObject resp = JSONObject.parseObject(response);
        if (resp.getInteger("status") != 0) {
            throw new RuntimeException("httpExecuteJob fail");
        }
    }
}
