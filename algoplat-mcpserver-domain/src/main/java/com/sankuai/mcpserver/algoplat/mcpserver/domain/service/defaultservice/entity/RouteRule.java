package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/8/20
 */
public class RouteRule {
    String swimlane;	// 泳道
    String cell;			// SET
    String grouptags;	// 业务分组

    public RouteRule() {}
    public RouteRule(String cell, String swimlane, String grouptags) {
        this.cell = cell;
        this.swimlane = swimlane;
        this.grouptags = grouptags;
    }
    public String getSwimlane() {
        return swimlane;
    }
    public void setSwimlane(String swimlane) {
        this.swimlane = swimlane;
    }
    public String getCell() {
        return cell;
    }
    public void setCell(String cell) {
        this.cell = cell;
    }
    public String getGrouptags() {
        return grouptags;
    }
    public void setGrouptags(String grouptags) {
        this.grouptags = grouptags;
    }
}
